#!/usr/bin/env python3
"""
AI Utility Orchestrator - Real Use Case Example
===============================================

This file demonstrates how to implement the AI Utility Orchestrator package
in a real-world scenario. It shows practical usage patterns for building
AI-powered applications with dynamic tool orchestration.

Author: <PERSON><PERSON><PERSON>
"""

import os
import json
from datetime import datetime
from ai_utility_orchestrator import (
    agent_executor,
    ToolRegistry,
    ContextManager,
    create_tool_from_function
)


class BusinessToolsExample:
    """Example implementation of business-specific tools."""
    
    def __init__(self):
        self.context_manager = ContextManager()
        self.tool_registry = ToolRegistry()
        self.setup_tools()
    
    def setup_tools(self):
        """Initialize and register AI-powered tools with zero hardcoding."""

        # Completely AI-driven tool with zero hardcoding
        def ai_universal_processor(params):
            """Universal AI-powered tool that handles any request using AI."""
            import openai
            import os

            # Extract the request
            request = params.get('request', '')
            context = params.get('context', '')
            data = params.get('data', {})

            # Create AI prompt for processing the request
            ai_prompt = f"""Process this request using AI intelligence:

Request: {request}
Context: {context}
Data: {data}

Provide a helpful, accurate response. For calculations, show your work. For analysis, provide insights. For content creation, be creative and professional."""

            try:
                # Make AI call to process the request
                client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": ai_prompt}],
                    temperature=0.3
                )

                return response.choices[0].message.content

            except Exception as e:
                return f"AI processing error: {str(e)}. Request was: {request}"
        
        # Register the universal AI-powered tool - completely dynamic
        tools_config = [
            (ai_universal_processor, "ai_universal_processor", "Universal AI-powered tool that dynamically handles any request", {
                "type": "object",
                "properties": {
                    "request": {"type": "string", "description": "Natural language description of what you need"},
                    "data": {"type": "object", "description": "Any data or parameters for the request"},
                    "context": {"type": "string", "description": "Additional context or requirements"}
                },
                "additionalProperties": True  # Allow any additional properties for maximum flexibility
            })
        ]
        
        for func, name, desc, schema in tools_config:
            tool = create_tool_from_function(func, name=name, description=desc, schema=schema)
            self.tool_registry.register_tool(tool)
    
    def create_agent_config(self):
        """Create comprehensive agent configuration."""
        return {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.1  # Low temperature for consistent JSON responses
            },
            "system_prompt": """You only return JSON. Format: {"selected_tool": "ai_universal_processor", "parameters": {"request": "..."}, "reasoning": "..."}. Never use "tool" or "input" keys. Only "selected_tool" and "parameters".""",
            "context_limit": 10,
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": True
            },
            "no_tools_prompt": """I understand your request: {user_input}

            I don't have a specific AI tool available for this request, but I can provide general assistance
            based on my knowledge.""",
            "tool_selection_prompt": """Available tools: {tools_text}

User request: "{user_input}"

RESPOND WITH EXACTLY THIS JSON STRUCTURE (no other text):
{{"selected_tool": "ai_universal_processor", "parameters": {{"request": "{user_input}"}}, "reasoning": "Processing with universal AI tool"}}

CRITICAL: Use these exact key names:
- "selected_tool" (NOT "tool")
- "parameters" (NOT "input")
- "reasoning"

NO additional text before or after the JSON.""",
            "tool_description_template": "• {name}: {description}",
            "error_response_template": "Business operation error with {tool_name}: {error}. Please check your parameters and try again.",
            "llm_error_message": "I encountered an issue processing your business request. Please try rephrasing or contact support.",
            "json_extraction": {
                "patterns": [
                    r'\{[^{}]*"selected_tool"[^{}]*"parameters"[^{}]*\}',
                    r'\{.*?"selected_tool".*?"parameters".*?\}',
                    r'```json\s*(\{.*?"selected_tool".*?\})\s*```',
                    r'\{[^{}]*"selected_tool"[^{}]*\}',
                    r'\{.*?\}'
                ]
            },
            "tools": self.tool_registry.get_tools()
        }


    def setup_business_context(self, user_id, company_name, role):
        """Setup business context for the session."""
        self.context_manager.set_context("user_id", user_id)
        self.context_manager.set_context("company", company_name)
        self.context_manager.set_context("user_role", role)
        self.context_manager.set_context("session_start", datetime.now().isoformat())
        self.context_manager.set_context("permissions", {
            "can_access_financial_data": role in ["manager", "admin", "analyst"],
            "can_modify_customer_data": role in ["manager", "admin"],
            "can_send_campaigns": role in ["marketing", "manager", "admin"]
        })
    
    def process_business_request(self, user_input, user_id="business_user"):
        """Process a business request using the AI orchestrator."""
        config = self.create_agent_config()
        
        result = agent_executor(
            user_input=user_input,
            config=config,
            user_id=user_id,
            context_manager=self.context_manager
        )
        
        # Log the interaction for business analytics
        self.context_manager.set_context("last_request", {
            "input": user_input,
            "tool_used": result.get("selected_tool"),
            "timestamp": datetime.now().isoformat()
        })
        
        return result


def main():
    """Demonstrate real-world usage of the AI Utility Orchestrator."""
    
    # Check for OpenAI API key
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: Please set your OPENAI_API_KEY environment variable")
        return
    
    # Initialize the business tools system
    business_system = BusinessToolsExample()
    
    # Setup business context
    business_system.setup_business_context(
        user_id="john_doe",
        company_name="TechCorp Solutions",
        role="manager"
    )
    
    print("AI Utility Orchestrator - Zero Hardcoding Demo")
    print("=" * 50)
    print("Universal AI-Powered Tool Available:")
    print("- AI Universal Processor: Handles ANY request dynamically")
    print("  * Mathematical calculations")
    print("  * Data processing and analysis")
    print("  * Communication and content creation")
    print("  * Business process management")
    print("  * Any other request you can imagine")
    print("\nCompletely AI-driven with ZERO hardcoded logic!")
    print("Type 'quit' to exit\n")
    
    # Interactive business assistant loop
    while True:
        try:
            user_input = input("Business Request: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Thank you for using AI Utility Orchestrator!")
                break
            
            if not user_input:
                continue
            
            print("\nProcessing your business request...")
            result = business_system.process_business_request(user_input)
            
            print(f"\nTool Selected: {result['selected_tool']}")
            if result.get('tool_parameters'):
                print(f"Parameters: {json.dumps(result['tool_parameters'], indent=2)}")
            print(f"Response: {result['final_response']}")
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n\nSession ended by user.")
            break
        except Exception as e:
            print(f"Error: {str(e)}")
            print("Please try again with a different request.")


if __name__ == "__main__":
    main()
